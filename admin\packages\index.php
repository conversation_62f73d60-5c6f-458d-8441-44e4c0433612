<?php
/**
 * Admin Packages Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create':
            $result = createPackage($_POST);
            if ($result['success']) {
                $_SESSION['success'] = 'Package created successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
            break;
            
        case 'update':
            $result = updatePackage($_POST['id'], $_POST);
            if ($result['success']) {
                $_SESSION['success'] = 'Package updated successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
            break;
            
        case 'delete':
            $result = deletePackage($_POST['id']);
            if ($result['success']) {
                $_SESSION['success'] = 'Package deleted successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
            break;
    }
    
    redirect('/admin/packages');
}

// Get packages with pagination
$page = (int)($_GET['page'] ?? 1);
$limit = 9;
$offset = ($page - 1) * $limit;
$search = sanitize($_GET['search'] ?? '');
$status = sanitize($_GET['status'] ?? '');

$whereClause = "WHERE 1=1";
$params = [];

if ($search) {
    $whereClause .= " AND (name LIKE ? OR description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status !== '') {
    $whereClause .= " AND is_active = ?";
    $params[] = (int)$status;
}

$packages = $database->fetchAll(
    "SELECT p.*, 
            COUNT(DISTINCT b.id) as total_bookings,
            COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as total_revenue
     FROM packages p
     LEFT JOIN bookings b ON p.id = b.package_id
     $whereClause
     GROUP BY p.id
     ORDER BY p.created_at DESC
     LIMIT $limit OFFSET $offset",
    $params
);

$totalPackages = $database->fetch(
    "SELECT COUNT(*) as count FROM packages $whereClause",
    $params
)['count'];

$totalPages = ceil($totalPackages / $limit);

// Get all services for package creation
$allServices = getActiveServices();

// Get package statistics
$stats = getPackageStats();

// Clean up any service-related session messages that might interfere with packages page
if (isset($_SESSION['success']) && (strpos($_SESSION['success'], 'Service') !== false || strpos($_SESSION['success'], 'service') !== false)) {
    unset($_SESSION['success']);
}
if (isset($_SESSION['error']) && (strpos($_SESSION['error'], 'Service') !== false || strpos($_SESSION['error'], 'service') !== false)) {
    unset($_SESSION['error']);
}
if (isset($_SESSION['delete_error'])) {
    $deleteError = json_decode($_SESSION['delete_error'], true);
    if ($deleteError && isset($deleteError['error']) && (strpos($deleteError['error'], 'Service') !== false || strpos($deleteError['error'], 'service') !== false)) {
        unset($_SESSION['delete_error']);
    }
}

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Packages Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-white">Packages Management</h1>
                            <p class="mt-1 text-sm text-gray-300">Create and manage service packages with special pricing</p>
                        </div>
                        <div class="mt-4 sm:mt-0">
                            <button onclick="openCreateModal()" class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                Create Package
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Message Display -->
                <?php if ($message): ?>
                    <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-300 truncate">Total Packages</dt>
                                    <dd class="text-lg font-medium text-white"><?= number_format($stats['total']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-300 truncate">Active Packages</dt>
                                    <dd class="text-lg font-medium text-white"><?= number_format($stats['active']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-300 truncate">Total Revenue</dt>
                                    <dd class="text-lg font-medium text-white"><?= formatCurrency($stats['total_revenue']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-300 truncate">Avg. Savings</dt>
                                    <dd class="text-lg font-medium text-white"><?= formatCurrency($stats['avg_savings']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                    <form method="GET" class="flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                                   placeholder="Search packages..." 
                                   class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>
                        <div>
                            <select name="status" class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">All Status</option>
                                <option value="1" <?= $status === '1' ? 'selected' : '' ?>>Active</option>
                                <option value="0" <?= $status === '0' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                        </div>
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Filter
                        </button>
                        <?php if ($search || $status !== ''): ?>
                            <a href="<?= getBasePath() ?>/admin/packages" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                Clear
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- Packages Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                    <?php foreach ($packages as $package): ?>
                        <?php 
                        $packageServices = getPackageServices($package['id']);
                        $originalPrice = array_sum(array_column($packageServices, 'price'));
                        $savings = $originalPrice - $package['price'];
                        $discountPercent = $originalPrice > 0 ? round(($savings / $originalPrice) * 100) : 0;
                        
                        // Calculate total duration from services
                        $servicesDuration = array_sum(array_column($packageServices, 'duration'));
                        // Use package_duration if set, otherwise use calculated duration
                        $totalDuration = $package['package_duration'] > 0 ? $package['package_duration'] : $servicesDuration;
                        ?>
                        <div class="bg-secondary-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group">
                            <!-- Package Image -->
                            <?php if (!empty($package['image'])): ?>
                                <?php
                                // Determine image URL - check if it's a URL or uploaded file
                                $imageUrl = filter_var($package['image'], FILTER_VALIDATE_URL)
                                    ? $package['image']
                                    : getBasePath() . '/uploads/packages/' . $package['image'];
                                ?>
                                <div class="h-48 bg-secondary-600 overflow-hidden">
                                    <img src="<?= htmlspecialchars($imageUrl) ?>"
                                         alt="<?= htmlspecialchars($package['name']) ?>"
                                         class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                                         onerror="this.parentElement.style.display='none'">
                                </div>
                            <?php endif; ?>

                            <div class="p-6">
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex-1">
                                        <h3 class="text-xl font-semibold text-white group-hover:text-salon-gold transition-colors mb-2">
                                            <?= htmlspecialchars($package['name']) ?>
                                        </h3>
                                        <?php if ($discountPercent > 0): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mb-2">
                                                Save <?= $discountPercent ?>%
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $package['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                        <?= $package['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </div>

                                <?php if ($package['description']): ?>
                                    <p class="text-gray-300 text-sm mb-4 line-clamp-2">
                                        <?= htmlspecialchars($package['description']) ?>
                                    </p>
                                <?php endif; ?>

                                <!-- Services List -->
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-gray-300 mb-2">Included Services:</h4>
                                    <div class="space-y-1">
                                        <?php foreach (array_slice($packageServices, 0, 3) as $service): ?>
                                            <div class="flex justify-between text-xs text-gray-400">
                                                <span><?= htmlspecialchars($service['name']) ?></span>
                                                <span>
                                                    <?php if ($service['price'] > 0): ?>
                                                        <?= formatCurrency($service['price']) ?>
                                                    <?php else: ?>
                                                        Custom
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        <?php endforeach; ?>
                                        <?php if (count($packageServices) > 3): ?>
                                            <div class="text-xs text-salon-gold">
                                                +<?= count($packageServices) - 3 ?> more services
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Pricing -->
                                <div class="mb-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <?php if ($savings > 0): ?>
                                                <div class="text-sm text-gray-400 line-through">
                                                    <?= formatCurrency($originalPrice) ?>
                                                </div>
                                            <?php endif; ?>
                                            <div class="text-2xl font-bold text-salon-gold">
                                                <?= formatCurrency($package['price']) ?>
                                            </div>
                                        </div>
                                        <?php if ($savings > 0): ?>
                                            <div class="text-right">
                                                <div class="text-sm text-green-400">Save</div>
                                                <div class="text-lg font-semibold text-green-400">
                                                    <?= formatCurrency($savings) ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Stats -->
                                <div class="grid grid-cols-3 gap-3 mb-4 text-center">
                                    <div>
                                        <div class="text-lg font-semibold text-white"><?= number_format($package['total_bookings']) ?></div>
                                        <div class="text-xs text-gray-400">Bookings</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-semibold text-salon-gold"><?= formatCurrency($package['total_revenue']) ?></div>
                                        <div class="text-xs text-gray-400">Revenue</div>
                                    </div>
                                    <div>
                                        <div class="text-lg font-semibold text-blue-400">
                                            <?php if ($totalDuration > 0): ?>
                                                <?= $totalDuration ?> min
                                            <?php else: ?>
                                                Custom
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-xs text-gray-400">
                                            <?php if ($package['package_duration'] > 0): ?>
                                                Package Duration
                                            <?php else: ?>
                                                Total Duration
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex gap-2">
                                    <button onclick="editPackage('<?= $package['id'] ?>')" 
                                            class="flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                                        Edit
                                    </button>
                                    <button onclick="viewPackage('<?= $package['id'] ?>')" 
                                            class="px-3 py-2 bg-purple-600 text-white rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors">
                                        View
                                    </button>
                                    <button onclick="deletePackage('<?= $package['id'] ?>', '<?= htmlspecialchars($package['name']) ?>')" 
                                            class="px-3 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 transition-colors">
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="bg-secondary-800 px-4 py-3 flex items-center justify-between border-t border-secondary-700 sm:px-6 rounded-lg">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>" 
                                   class="relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                    Previous
                                </a>
                            <?php endif; ?>
                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>" 
                                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                    Next
                                </a>
                            <?php endif; ?>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-300">
                                    Showing <span class="font-medium"><?= $offset + 1 ?></span> to <span class="font-medium"><?= min($offset + $limit, $totalPackages) ?></span> of <span class="font-medium"><?= $totalPackages ?></span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>" 
                                           class="relative inline-flex items-center px-4 py-2 border text-sm font-medium <?= $i === $page ? 'z-10 bg-salon-gold border-salon-gold text-black' : 'bg-secondary-700 border-secondary-600 text-gray-300 hover:bg-secondary-600' ?>">
                                            <?= $i ?>
                                        </a>
                                    <?php endfor; ?>
                                </nav>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Create/Edit Package Modal -->
<div id="packageModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-secondary-800 rounded-lg w-full max-w-4xl max-h-screen overflow-y-auto">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 id="modalTitle" class="text-xl font-bold text-white">Create Package</h2>
                <button onclick="closeModal()" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <form id="packageForm" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" id="formAction" value="create">
                <input type="hidden" name="id" id="packageId">
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Left Column -->
                    <div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-300 mb-2">Package Name *</label>
                            <input type="text" name="name" id="packageName" required 
                                   class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                            <textarea name="description" id="packageDescription" rows="3" 
                                      class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"></textarea>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-300 mb-2">Package Price (TSH) *</label>
                            <input type="number" name="price" id="packagePrice" step="1" min="0" required
                                   class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                            <p class="text-xs text-gray-400 mt-1">Set a discounted price for the package (whole numbers only)</p>
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-300 mb-2">Package Duration (minutes)</label>
                            <input type="number" name="package_duration" id="packageDuration" step="15" min="0"
                                   class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                            <p class="text-xs text-gray-400 mt-1">Optional: Set overall duration for the entire package (useful for custom packages)</p>
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-300 mb-2">Package Image</label>

                            <!-- Image Option Toggle -->
                            <div class="flex gap-4 mb-3">
                                <label class="flex items-center">
                                    <input type="radio" name="image_option" value="url" id="imageOptionUrl" checked
                                           class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                                    <span class="ml-2 text-sm text-gray-300">Image URL</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="image_option" value="upload" id="imageOptionUpload"
                                           class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                                    <span class="ml-2 text-sm text-gray-300">Upload Image</span>
                                </label>
                            </div>

                            <!-- URL Input -->
                            <div id="imageUrlSection">
                                <input type="url" name="image_url" id="packageImageUrl"
                                       placeholder="https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400"
                                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <p class="text-xs text-gray-400 mt-1">Enter a direct image URL (e.g., from images.unsplash.com, not unsplash.com/photos/)</p>
                                <div id="urlValidationMessage" class="text-xs mt-1 hidden"></div>
                            </div>

                            <!-- File Upload -->
                            <div id="imageUploadSection" class="hidden">
                                <div class="flex items-center justify-center w-full">
                                    <label for="packageImageFile" class="flex flex-col items-center justify-center w-full h-32 border-2 border-secondary-600 border-dashed rounded-lg cursor-pointer bg-secondary-700 hover:bg-secondary-600 transition-colors">
                                        <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                            <svg class="w-8 h-8 mb-4 text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                            </svg>
                                            <p class="mb-2 text-sm text-gray-400"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                            <p class="text-xs text-gray-400">PNG, JPG, GIF, WebP (MAX. 5MB)</p>
                                        </div>
                                        <input id="packageImageFile" name="image_file" type="file" accept="image/*" class="hidden" />
                                    </label>
                                </div>
                                <p class="text-xs text-gray-400 mt-1">Upload an image file for the package</p>
                            </div>

                            <!-- Image Preview -->
                            <div id="imagePreview" class="mt-3 hidden">
                                <img id="previewImg" src="" alt="Package preview" class="w-full h-32 object-cover rounded-lg border border-secondary-600">
                                <button type="button" onclick="clearImagePreview()" class="mt-2 text-xs text-red-400 hover:text-red-300">Remove Image</button>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="flex items-center">
                                <input type="checkbox" name="is_active" id="packageActive" value="1" checked
                                       class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                                <span class="ml-2 text-sm text-gray-300">Package is active</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Right Column - Services Selection -->
                    <div>
                        <div class="flex items-center justify-between mb-2">
                            <label class="block text-sm font-medium text-gray-300">Package Services *</label>
                            <div class="flex gap-2">
                                <button type="button" onclick="toggleServiceType('catalog')" id="catalogServicesBtn" 
                                        class="px-3 py-1 text-xs rounded-full bg-salon-gold text-black font-medium">
                                    From Catalog
                                </button>
                                <button type="button" onclick="toggleServiceType('manual')" id="manualServicesBtn" 
                                        class="px-3 py-1 text-xs rounded-full bg-secondary-600 text-gray-300 hover:bg-secondary-500">
                                    Manual Entry
                                </button>
                            </div>
                        </div>
                        
                        <!-- Catalog Services -->
                        <div id="catalogServicesSection" class="bg-secondary-700 rounded-lg p-4 max-h-80 overflow-y-auto">
                            <div class="space-y-2">
                                <?php foreach ($allServices as $service): ?>
                                    <label class="flex items-center p-2 hover:bg-secondary-600 rounded">
                                        <input type="checkbox" name="services[]" value="<?= $service['id'] ?>" 
                                               data-price="<?= $service['price'] ?>" data-type="catalog"
                                               class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800 service-checkbox">
                                        <div class="ml-3 flex-1">
                                            <div class="text-sm font-medium text-white"><?= htmlspecialchars($service['name']) ?></div>
                                            <div class="text-xs text-gray-400">
                                                <?php if ($service['price'] > 0): ?>
                                                    <?= formatCurrency($service['price']) ?>
                                                    <?php if ($service['duration'] > 0): ?> • <?php endif; ?>
                                                <?php endif; ?>
                                                <?php if ($service['duration'] > 0): ?>
                                                    <?= $service['duration'] ?> min
                                                <?php endif; ?>
                                                <?php if ($service['price'] == 0 && $service['duration'] == 0): ?>
                                                    Custom Service
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </label>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Manual Services Entry -->
                        <div id="manualServicesSection" class="bg-secondary-700 rounded-lg p-4 hidden">
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <h4 class="text-sm font-medium text-white">Custom Services</h4>
                                    <button type="button" onclick="addManualService()" 
                                            class="bg-salon-gold text-black px-3 py-1 rounded text-xs font-medium hover:bg-gold-light">
                                        Add Service
                                    </button>
                                </div>
                                
                                <div id="manualServicesList" class="space-y-3">
                                    <!-- Manual services will be added here dynamically -->
                                </div>
                                
                                <div class="text-xs text-gray-400 text-center py-2">
                                    Click "Add Service" to manually enter custom services for this package
                                </div>
                            </div>
                        </div>
                        
                        <!-- Price Calculator -->
                        <div class="mt-4 p-4 bg-secondary-700 rounded-lg">
                            <div class="flex justify-between text-sm text-gray-300 mb-2">
                                <span>Original Price:</span>
                                <span id="originalPrice">TSH 0</span>
                            </div>
                            <div class="flex justify-between text-sm text-gray-300 mb-2">
                                <span>Package Price:</span>
                                <span id="packagePriceDisplay">TSH 0</span>
                            </div>
                            <div class="flex justify-between text-sm font-medium text-green-400">
                                <span>Savings:</span>
                                <span id="savingsAmount">TSH 0</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex gap-4 mt-6">
                    <button type="submit" class="flex-1 bg-salon-gold text-black py-2 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                        Save Package
                    </button>
                    <button type="button" onclick="closeModal()" class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


<script>
// Modal functions
function openCreateModal() {
    document.getElementById('modalTitle').textContent = 'Create Package';
    document.getElementById('formAction').value = 'create';
    document.getElementById('packageForm').reset();
    document.getElementById('packageActive').checked = true;

    // Reset image options to URL by default
    document.getElementById('imageOptionUrl').checked = true;
    toggleImageOption();
    clearImagePreview();

    // Clear manual services and reset counter
    document.getElementById('manualServicesList').innerHTML = '';
    manualServiceCounter = 0;

    // Clear all service checkboxes
    document.querySelectorAll('.service-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });

    // Reset to catalog services tab
    toggleServiceType('catalog');

    updatePriceCalculator();
    document.getElementById('packageModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('packageModal').classList.add('hidden');
}

function editPackage(packageId) {
    // Fetch package data and populate form
    fetch(`<?= getBasePath() ?>/api/admin/packages/get.php?id=${packageId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(package => {
            document.getElementById('modalTitle').textContent = 'Edit Package';
            document.getElementById('formAction').value = 'update';
            document.getElementById('packageId').value = package.id;
            document.getElementById('packageName').value = package.name;
            document.getElementById('packageDescription').value = package.description || '';
            document.getElementById('packagePrice').value = package.price;
            document.getElementById('packageDuration').value = package.package_duration || '';
            document.getElementById('packageActive').checked = package.is_active;

            // Handle image display - determine if it's URL or uploaded file
            const imageValue = package.image || '';

            const urlOption = document.getElementById('imageOptionUrl');
            const uploadOption = document.getElementById('imageOptionUpload');
            const urlInput = document.getElementById('packageImageUrl');

            if (imageValue) {
                if (imageValue.startsWith('http://') || imageValue.startsWith('https://')) {
                    // It's a URL
                    if (urlOption) urlOption.checked = true;
                    if (urlInput) urlInput.value = imageValue;
                    toggleImageOption();
                    updateImagePreview(imageValue);
                } else {
                    // It's an uploaded file path
                    if (uploadOption) uploadOption.checked = true;
                    toggleImageOption();
                    // Show preview for uploaded file
                    const basePath = '<?= getBasePath() ?>';
                    const fullImageUrl = basePath + '/uploads/packages/' + imageValue;
                    updateImagePreview(fullImageUrl);
                }
            } else {
                // No image
                if (urlOption) urlOption.checked = true;
                if (urlInput) urlInput.value = '';
                toggleImageOption();
                clearImagePreview();
            }
            
            // Clear all checkboxes first, then check selected catalog services
            document.querySelectorAll('.service-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            
            document.querySelectorAll('.service-checkbox').forEach(checkbox => {
                const serviceType = checkbox.dataset.type;
                if (serviceType === 'catalog') {
                    checkbox.checked = package.services.some(s => s.id === checkbox.value && s.service_type === 'catalog');
                }
            });

            // Clear and populate manual services
            document.getElementById('manualServicesList').innerHTML = '';
            manualServiceCounter = 0;
            
            const manualServices = package.services.filter(s => s.service_type === 'manual');
            manualServices.forEach(service => {
                addManualService();
                const lastService = document.querySelector('#manualServicesList .bg-secondary-600:last-child');
                if (lastService) {
                    lastService.querySelector('input[name*="[name]"]').value = service.name;
                    lastService.querySelector('input[name*="[price]"]').value = service.price > 0 ? service.price : '';
                    lastService.querySelector('input[name*="[duration]"]').value = service.duration > 0 ? service.duration : '';
                    lastService.querySelector('input[name*="[description]"]').value = service.description || '';
                }
            });

            // Show appropriate service section
            if (manualServices.length > 0) {
                toggleServiceType('manual');
            } else {
                toggleServiceType('catalog');
            }
            
            updatePriceCalculator();
            document.getElementById('packageModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error fetching package:', error);
            alert('Error loading package data. Please check the console for details and try again.');
        });
}

function viewPackage(packageId) {
    window.location.href = `<?= getBasePath() ?>/admin/packages/view.php?id=${packageId}`;
}

function deletePackage(packageId, packageName) {
    if (confirm(`Are you sure you want to delete "${packageName}"? This action cannot be undone.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${packageId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Price calculator
function updatePriceCalculator() {
    let originalPrice = 0;

    // Calculate price from catalog services
    const checkboxes = document.querySelectorAll('.service-checkbox:checked');
    checkboxes.forEach(checkbox => {
        originalPrice += parseInt(checkbox.dataset.price);
    });

    // Calculate price from manual services (only include if price is specified)
    const manualPrices = document.querySelectorAll('.manual-service-price');
    manualPrices.forEach(input => {
        const price = parseInt(input.value) || 0;
        if (price > 0) {
            originalPrice += price;
        }
    });

    const packagePrice = parseInt(document.getElementById('packagePrice').value) || 0;
    const savings = Math.max(0, originalPrice - packagePrice);

    document.getElementById('originalPrice').textContent = formatCurrency(originalPrice);
    document.getElementById('packagePriceDisplay').textContent = formatCurrency(packagePrice);
    document.getElementById('savingsAmount').textContent = formatCurrency(savings);
}

// Event listeners
document.querySelectorAll('.service-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updatePriceCalculator);
});

document.getElementById('packagePrice').addEventListener('input', updatePriceCalculator);

function formatCurrency(amount) {
    return 'TSH ' + parseInt(amount).toLocaleString();
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
});

// Close modal on backdrop click
document.getElementById('packageModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// Manual Services Functions
let manualServiceCounter = 0;

function addManualService() {
    manualServiceCounter++;
    const serviceId = 'manual_' + manualServiceCounter;
    
    const serviceHtml = `
        <div class="bg-secondary-600 rounded-lg p-4" id="service_${serviceId}">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-3">
                <div>
                    <label class="block text-xs font-medium text-gray-300 mb-1">Service Name *</label>
                    <input type="text" name="manual_services[${serviceId}][name]" required
                           class="w-full px-2 py-1 text-sm bg-secondary-500 border border-secondary-400 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-salon-gold"
                           placeholder="e.g., VIP Consultation">
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-300 mb-1">Price (TSH)</label>
                    <input type="number" name="manual_services[${serviceId}][price]" min="0" step="1"
                           class="w-full px-2 py-1 text-sm bg-secondary-500 border border-secondary-400 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-salon-gold manual-service-price"
                           placeholder="Optional" onchange="updatePriceCalculator()">
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-300 mb-1">Duration (min)</label>
                    <input type="number" name="manual_services[${serviceId}][duration]" min="1" step="1"
                           class="w-full px-2 py-1 text-sm bg-secondary-500 border border-secondary-400 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-salon-gold"
                           placeholder="Optional">
                </div>
                <div class="flex items-end">
                    <button type="button" onclick="removeManualService('${serviceId}')" 
                            class="w-full bg-red-600 text-white px-2 py-1 rounded text-xs hover:bg-red-700 transition-colors">
                        Remove
                    </button>
                </div>
            </div>
            <div class="mt-2">
                <label class="block text-xs font-medium text-gray-300 mb-1">Description (optional)</label>
                <input type="text" name="manual_services[${serviceId}][description]"
                       class="w-full px-2 py-1 text-sm bg-secondary-500 border border-secondary-400 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-salon-gold"
                       placeholder="Brief description of the service">
            </div>
        </div>
    `;
    
    document.getElementById('manualServicesList').insertAdjacentHTML('beforeend', serviceHtml);
    updatePriceCalculator();
}

function removeManualService(serviceId) {
    document.getElementById('service_' + serviceId).remove();
    updatePriceCalculator();
}

// Service Type Toggle Functions
function toggleServiceType(type) {
    const catalogBtn = document.getElementById('catalogServicesBtn');
    const manualBtn = document.getElementById('manualServicesBtn');
    const catalogSection = document.getElementById('catalogServicesSection');
    const manualSection = document.getElementById('manualServicesSection');

    if (type === 'catalog') {
        catalogBtn.className = 'px-3 py-1 text-xs rounded-full bg-salon-gold text-black font-medium';
        manualBtn.className = 'px-3 py-1 text-xs rounded-full bg-secondary-600 text-gray-300 hover:bg-secondary-500';
        catalogSection.classList.remove('hidden');
        manualSection.classList.add('hidden');
    } else {
        manualBtn.className = 'px-3 py-1 text-xs rounded-full bg-salon-gold text-black font-medium';
        catalogBtn.className = 'px-3 py-1 text-xs rounded-full bg-secondary-600 text-gray-300 hover:bg-secondary-500';
        manualSection.classList.remove('hidden');
        catalogSection.classList.add('hidden');
    }
}

// Image option toggle functionality
function toggleImageOption() {
    const urlOption = document.getElementById('imageOptionUrl');
    const uploadOption = document.getElementById('imageOptionUpload');
    const urlSection = document.getElementById('imageUrlSection');
    const uploadSection = document.getElementById('imageUploadSection');

    if (urlOption.checked) {
        urlSection.classList.remove('hidden');
        uploadSection.classList.add('hidden');
        // Clear upload input
        document.getElementById('packageImageFile').value = '';
    } else {
        urlSection.classList.add('hidden');
        uploadSection.classList.remove('hidden');
        // Clear URL input
        document.getElementById('packageImageUrl').value = '';
    }

    // Clear preview when switching
    clearImagePreview();
}

// Image preview functionality
function updateImagePreview(imageUrl) {
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');

    if (imageUrl && isValidImageUrl(imageUrl)) {
        previewImg.src = imageUrl;
        imagePreview.classList.remove('hidden');
    } else {
        imagePreview.classList.add('hidden');
        previewImg.src = '';
    }
}

function updateImagePreviewFromFile(file) {
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');

    if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            imagePreview.classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    } else {
        clearImagePreview();
    }
}

function clearImagePreview() {
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');

    imagePreview.classList.add('hidden');
    previewImg.src = '';
}

function isValidImageUrl(url) {
    try {
        new URL(url);
        // Check for image file extensions or known image hosting patterns
        return /\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i.test(url) ||
               /images\.unsplash\.com/.test(url) ||
               /source\.unsplash\.com/.test(url) ||
               /cdn\./.test(url) ||
               /imgur\.com/.test(url);
    } catch {
        return false;
    }
}

function validateImageUrl(url) {
    if (!url) return { valid: true, message: '' };

    try {
        new URL(url);

        // Check for common mistakes
        if (url.includes('unsplash.com/photos/')) {
            return {
                valid: false,
                message: 'Please use the direct image URL from Unsplash (images.unsplash.com), not the page URL'
            };
        }

        if (url.includes('pinterest.com') || url.includes('google.com/search')) {
            return {
                valid: false,
                message: 'Please use a direct image URL, not a search or social media page'
            };
        }

        // Check for valid image patterns
        if (/\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i.test(url) ||
            /images\.unsplash\.com/.test(url) ||
            /source\.unsplash\.com/.test(url) ||
            /cdn\./.test(url) ||
            /imgur\.com/.test(url)) {
            return { valid: true, message: '' };
        }

        return {
            valid: false,
            message: 'URL should point to an image file (.jpg, .png, .gif, .webp) or a known image hosting service'
        };

    } catch {
        return {
            valid: false,
            message: 'Please enter a valid URL starting with http:// or https://'
        };
    }
}

// Check for edit parameter in URL
document.addEventListener('DOMContentLoaded', function() {
    // Clear any cached alert content on page load
    const alertModal = document.getElementById('customAlert');
    if (alertModal) {
        alertModal.classList.add('hidden');
    }

    // Clear any alert content that might be cached from other pages
    const alertTitle = document.getElementById('alertTitle');
    const alertMessage = document.getElementById('alertMessage');
    if (alertTitle) alertTitle.textContent = '';
    if (alertMessage) alertMessage.innerHTML = '';

    const urlParams = new URLSearchParams(window.location.search);
    const editId = urlParams.get('edit');
    if (editId) {
        editPackage(editId);
        // Clean up URL
        window.history.replaceState({}, document.title, window.location.pathname + window.location.search.replace(/[?&]edit=[^&]*/, ''));
    }

    // Add form submission debugging
    const packageForm = document.getElementById('packageForm');
    if (packageForm) {
        packageForm.addEventListener('submit', function(e) {
            console.log('Form submitted');
            const formData = new FormData(this);
            console.log('Form data:', Object.fromEntries(formData));

            // Check if services are selected (catalog or manual)
            const selectedCatalogServices = formData.getAll('services[]');
            const manualServices = document.querySelectorAll('#manualServicesList .bg-secondary-600');
            const totalServices = selectedCatalogServices.length + manualServices.length;
            
            console.log('Selected catalog services:', selectedCatalogServices);
            console.log('Manual services count:', manualServices.length);
            console.log('Total services:', totalServices);

            if (totalServices < 2) {
                e.preventDefault();
                alert('Please select at least 2 services (from catalog or manual entry) for the package.');
                return false;
            }

            // Validate manual services have required fields (only name is required)
            let manualServiceValid = true;
            manualServices.forEach(serviceDiv => {
                const nameInput = serviceDiv.querySelector('input[name*="[name]"]');
                
                if (!nameInput.value.trim()) {
                    manualServiceValid = false;
                }
            });

            if (!manualServiceValid) {
                e.preventDefault();
                alert('Please fill in the service name for all manual services.');
                return false;
            }
        });
    }

    // Add image option toggle event listeners
    const imageOptionUrl = document.getElementById('imageOptionUrl');
    const imageOptionUpload = document.getElementById('imageOptionUpload');
    if (imageOptionUrl && imageOptionUpload) {
        imageOptionUrl.addEventListener('change', toggleImageOption);
        imageOptionUpload.addEventListener('change', toggleImageOption);
    }

    // Add image URL preview event listener with validation
    const packageImageUrl = document.getElementById('packageImageUrl');
    if (packageImageUrl) {
        packageImageUrl.addEventListener('input', function() {
            const url = this.value;
            const validation = validateImageUrl(url);
            const messageDiv = document.getElementById('urlValidationMessage');

            if (url && !validation.valid) {
                messageDiv.textContent = validation.message;
                messageDiv.className = 'text-xs mt-1 text-red-400';
                messageDiv.classList.remove('hidden');
                clearImagePreview();
            } else {
                messageDiv.classList.add('hidden');
                updateImagePreview(url);
            }
        });
    }

    // Add file upload preview event listener
    const packageImageFile = document.getElementById('packageImageFile');
    if (packageImageFile) {
        packageImageFile.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                updateImagePreviewFromFile(this.files[0]);
            }
        });
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
